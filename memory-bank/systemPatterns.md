# HaoPray 系统模式

## 整体架构设计
HaoPray 采用分层架构设计，将系统分为表现层、业务逻辑层和数据访问层，各层之间通过清晰的接口进行通信。

## 核心设计模式

### MVP 模式 (Model-View-Presenter)
项目主要采用 MVP 架构模式：
- **View 层**：Activity 和 Fragment，负责 UI 展示和用户交互
- **Presenter 层**：处理业务逻辑，作为 View 和 Model 的中介
- **Model 层**：数据访问对象，负责与数据库和网络交互

### 单例模式
核心管理器类采用单例模式确保全局唯一实例：
- `HaoPrayManager`：祷告数据管理器
- `HaoGroupManager`：分组数据管理器
- `SQLiteHelper`：数据库帮助类
- `SocketManager`：Socket 连接管理器

### 代理模式
大量使用代理模式来增强系统功能：
- `RecyclerViewProxy`：RecyclerView 功能增强代理
- `ViewProxy`：View 组件功能增强代理
- `PopupWindowProxy`：弹窗功能增强代理
- `InputStreamProxy` 和 `OutputStreamProxy`：IO 流功能增强代理

### 工厂模式
- 数据库 Cursor 处理采用代理工厂模式
- 对话框创建采用工厂模式

## 数据管理层设计

### 数据库设计
- **SQLiteHelper**：数据库核心管理类，负责表结构创建和版本升级
- **HaoPrayManager**：祷告数据操作管理器
- **HaoGroupManager**：分组数据操作管理器
- **CursorProxy**：数据库游标代理，提供增强的数据访问功能

### 数据同步设计
- **SocketManager**：Socket 连接管理，实现局域网设备发现
- **DeviceDiscoveryManager**：设备发现管理器
- **DatabaseSyncManager**：数据库同步管理器
- **EnhancedSocketManager**：增强的 Socket 管理器

## UI 组件设计

### 基础组件
- **BaseActivity**：所有 Activity 的基类，提供通用功能
- **BaseDialog**：所有对话框的基类
- **BaseFrameLayout**：基础布局组件

### 自定义视图
- **自定义 View**：位于 `com.haoxueren.view` 包下
- **自定义控件**：位于 `com.haoxueren.widget` 包下
- **对话框组件**：位于 `com.haoxueren.dialog` 包下

### Jetpack Compose 集成
- **StatisticsActivity**：使用 Jetpack Compose 构建的统计界面示例
- 逐步迁移传统 UI 到 Compose

## 网络层设计
- **OkHttpManager**：HTTP 请求管理器
- **BmobService**：Bmob 后端服务集成
- **QueryBuilder**：查询构建器

## 工具类设计
- **Utils 包**：包含各种工具类，如日期处理、网络诊断、设备信息等
- **Helper 包**：包含辅助类，如生命周期观察者、页面加载帮助类
- **Proxy 包**：包含各种代理类，用于功能增强

## 安全层设计
- **FingerprintDialog**：指纹验证对话框
- **AuthCodeDialog**：授权码对话框
- **数据加密工具**：数据传输和存储加密

## 核心组件关系

### 数据流向
```
UI 层 (Activity/Fragment) 
  ↓
Presenter 层 (业务逻辑处理)
  ↓
Model 层 (HaoPrayManager/HaoGroupManager)
  ↓
数据访问层 (SQLiteHelper/CursorProxy)
  ↓
SQLite 数据库
```

### 同步流向
```
设置界面
  ↓
SocketManager/DatabaseSyncManager
  ↓
局域网设备发现/远程服务器同步
  ↓
数据传输 (加密)
  ↓
目标设备数据库
```

## 关键实现路径

### 祷告记录创建流程
1. 用户在主界面输入祷告内容
2. MainActivity 调用 DataService 处理数据
3. DataService 调用 HaoPrayManager 保存到数据库
4. 更新 UI 显示新创建的祷告记录

### 数据同步流程
1. 用户在设置界面触发同步
2. SettingsActivity 调用 SocketManager
3. SocketManager 发现局域网设备
4. DatabaseSyncManager 执行数据同步
5. 显示同步结果给用户

### 分组管理流程
1. 用户进入分组管理界面 (AllGroupActivity)
2. 从数据库加载分组数据
3. 支持分组的增删改查操作
4. 实现分组排序和长按编辑功能
