# HaoPray 项目简介

## 项目概述
HaoPray 是一个 Android 祷告应用，旨在帮助用户记录、管理和跟踪他们的祷告事项。该应用提供了完整的祷告生命周期管理，包括祷告创建、分组管理、数据同步和统计分析等功能。

## 核心功能
- **祷告记录管理**：创建、编辑、删除祷告记录
- **分组管理**：将祷告按不同分组进行组织和管理
- **数据同步**：支持本地 SQLite 数据库与远程服务器的数据同步
- **统计分析**：提供祷告数据的统计和分析功能
- **安全保护**：支持指纹验证等安全特性
- **备份恢复**：支持本地数据备份和恢复

## 项目目标
- 为用户提供便捷的祷告记录和管理工具
- 实现跨设备的数据同步功能
- 提供直观的用户界面和良好的用户体验
- 确保数据安全和隐私保护

## 技术平台
- Android 原生应用开发
- SQLite 本地数据库存储
- Java/Kotlin 混合开发
- 支持 Jetpack Compose 现代 UI 框架
