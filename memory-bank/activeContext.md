# HaoPray 活跃上下文

## 当前工作重点
- 维护和优化现有的祷告记录管理功能
- 完善数据同步机制，提升同步稳定性和效率
- 优化用户界面体验，特别是主界面和分组管理界面
- 增强数据安全特性，确保用户隐私保护

## 最近变更和更新
- 项目结构分析完成，明确了各模块的职责和关系
- 设置界面功能完善，包含 API 模式切换、指纹验证、数据备份同步等
- 分组管理界面功能实现，支持分组的刷新、排序和编辑
- Jetpack Compose 集成开始，统计界面已使用 Compose 重构

## 活跃决策和考虑
- 保持 Java/Kotlin 混合开发模式，新功能优先使用 Kotlin
- 逐步迁移 UI 到 Jetpack Compose，但保持向后兼容性
- 数据同步机制采用双模式：局域网 Socket 同步 + 远程服务器同步
- 安全特性持续增强，包括指纹验证和数据加密

## 重要模式和偏好
- 遵循 MVP 架构模式，保持代码结构清晰
- 大量使用代理模式来增强系统功能和可维护性
- 采用单例模式管理核心数据管理器
- 工具类设计遵循静态方法模式，便于调用和测试

## 项目学习和洞察
- 项目具有良好的模块化设计，各功能模块职责清晰
- 代理模式的广泛使用提高了代码的可扩展性和可维护性
- 数据同步是项目的核心难点，需要持续优化和完善
- 安全性设计较为完善，但仍需关注数据传输和存储的安全性

## 当前技术债务
- 部分旧代码仍使用传统的 View 系统，需要逐步迁移到 Compose
- 数据同步机制在复杂网络环境下可能存在稳定性问题
- 错误处理机制需要进一步完善和统一
- 测试覆盖率有待提升，特别是集成测试

## 开发约定
- 新功能开发优先使用 Kotlin 语言
- UI 界面重构优先考虑 Jetpack Compose 实现
- 数据库操作统一通过 Manager 类进行，避免直接操作 SQLite
- 网络请求统一通过 OkHttpManager 处理
- 安全相关功能必须经过充分测试和验证
