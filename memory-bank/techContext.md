# HaoPray 技术上下文

## 技术栈概述
HaoPray 采用 Android 原生开发技术栈，结合传统 Java 和现代 Kotlin 混合开发模式，同时引入 Jetpack Compose 现代 UI 框架。

## 核心技术组件

### 编程语言
- **Java**：项目主体使用 Java 开发
- **Kotlin**：部分新功能采用 Kotlin 实现，特别是 Jetpack Compose 相关功能

### 数据存储
- **SQLite**：本地数据库存储，用于保存祷告记录、分组信息等核心数据
- **SharedPreferences**：用于存储应用配置和用户偏好设置

### 网络通信
- **OkHttp**：HTTP 客户端，用于与服务器进行数据同步
- **自定义 Socket 管理**：实现局域网内的设备发现和数据同步

### UI 框架
- **传统 View 系统**：项目主体使用传统的 Android View 系统
- **Jetpack Compose**：新界面采用 Jetpack Compose 构建，如统计界面

### 核心框架和库
- **Android SDK**：基础 Android 开发框架
- **SQLiteStudioRemote**：第三方 SQLite 管理库
- **自定义工具类**：包含大量自定义的工具类和帮助类

## 开发环境要求
- **Android Studio**：主要开发 IDE
- **Gradle**：构建工具
- **Java 8+**：Java 开发环境
- **Android SDK**：API 级别根据项目需求确定

## 项目架构模式
- **MVP 模式**：大部分功能采用 Model-View-Presenter 模式
- **工具类模式**：大量静态工具类提供通用功能
- **单例模式**：数据库管理器等核心组件采用单例模式

## 安全特性
- **指纹验证**：基于 Android 指纹 API 实现生物识别验证
- **数据加密**：敏感数据传输和存储时的加密处理

## 同步机制
- **局域网同步**：通过 Socket 实现局域网内的设备自动发现和数据同步
- **远程服务器同步**：支持与自定义服务器进行数据同步
- **数据备份**：支持本地数据库备份到文件

## 测试策略
- **单元测试**：使用 JUnit 进行核心逻辑测试
- **Instrumentation 测试**：UI 自动化测试
- **手动测试**：功能测试和用户体验测试

## 构建和部署
- **Gradle 构建系统**：项目构建和依赖管理
- **ProGuard**：代码混淆和优化
- **多环境配置**：支持不同的构建变体
