# BottomSheetDialog 键盘适配指南

## 概述

本项目为 BottomSheetDialog 提供了完整的软键盘适配解决方案，确保在软键盘弹出时对话框能够自动调整位置，避免底部操作按钮被遮挡。

## 核心组件

### 1. KeyboardUtils - 键盘工具类

提供键盘状态监听和管理功能：

```kotlin
// 添加键盘监听
val listener = KeyboardUtils.addKeyboardVisibilityListener(activity, object : KeyboardUtils.KeyboardVisibilityListener {
    override fun onKeyboardShow(keyboardHeight: Int) {
        // 键盘显示时的处理逻辑
    }
    
    override fun onKeyboardHide() {
        // 键盘隐藏时的处理逻辑
    }
})

// 移除监听
KeyboardUtils.removeKeyboardVisibilityListener(rootView, listener)
```

### 2. BottomSheetKeyboardAdapter - 键盘适配器

专门为 BottomSheetDialog 设计的键盘适配器：

```kotlin
val dialog = BottomSheetDialog(context, R.style.BottomSheetDialogTheme)
val adapter = BottomSheetKeyboardAdapter(dialog)
adapter.setupKeyboardAdaptation()
```

## 使用方法

### 在现有对话框中集成

以 `DatabaseRecordEditDialog` 为例：

```kotlin
class DatabaseRecordEditDialog(
    private val context: Context,
    private val record: DatabaseRecord,
    private val tableName: String,
    private val onSaveSuccess: () -> Unit
) {
    private lateinit var dialog: BottomSheetDialog
    private var keyboardAdapter: BottomSheetKeyboardAdapter? = null
    
    fun show() {
        dialog = BottomSheetDialog(context, R.style.BottomSheetDialogTheme)
        val view = LayoutInflater.from(context).inflate(R.layout.dialog_database_record_edit, null)
        dialog.setContentView(view)

        // 设置键盘适配
        keyboardAdapter = BottomSheetKeyboardAdapter(dialog)
        keyboardAdapter?.setupKeyboardAdaptation()

        // 其他初始化代码...
        dialog.show()
    }
    
    fun dismiss() {
        keyboardAdapter = null // 清理键盘适配器
        if (::dialog.isInitialized && dialog.isShowing) {
            dialog.dismiss()
        }
    }
}
```

### 自定义键盘适配行为

如果需要自定义键盘适配行为，可以直接使用 KeyboardUtils：

```kotlin
class CustomBottomSheetDialog {
    private var keyboardListener: ViewTreeObserver.OnGlobalLayoutListener? = null
    
    private fun setupCustomKeyboardAdaptation() {
        keyboardListener = KeyboardUtils.addKeyboardVisibilityListener(
            rootView,
            object : KeyboardUtils.KeyboardVisibilityListener {
                override fun onKeyboardShow(keyboardHeight: Int) {
                    // 自定义键盘显示处理
                    adjustDialogPosition(keyboardHeight)
                }
                
                override fun onKeyboardHide() {
                    // 自定义键盘隐藏处理
                    resetDialogPosition()
                }
            }
        )
    }
    
    private fun adjustDialogPosition(keyboardHeight: Int) {
        // 实现自定义的位置调整逻辑
    }
    
    private fun resetDialogPosition() {
        // 实现自定义的位置重置逻辑
    }
}
```

## 功能特性

### 1. 自动位置调整
- 检测软键盘弹出时的高度
- 计算对话框底部是否被遮挡
- 自动向上移动对话框避免遮挡

### 2. 平滑动画
- 使用 ValueAnimator 实现平滑的位置过渡
- 默认动画时长 300ms
- 使用 DecelerateInterpolator 提供自然的动画效果

### 3. 智能检测
- 设置合理的键盘高度阈值（150dp）
- 避免误判导航栏等系统UI为键盘
- 支持不同屏幕尺寸和键盘高度

### 4. 资源管理
- 自动清理监听器避免内存泄漏
- 在对话框关闭时自动恢复原始状态
- 取消进行中的动画避免异常

## 兼容性

### Android 版本支持
- 最低支持 Android API 21 (Android 5.0)
- 完全兼容 Android 14 及以下版本

### 屏幕适配
- 支持各种屏幕尺寸和分辨率
- 兼容横屏和竖屏模式
- 适配不同的键盘高度

### 主题支持
- 兼容浅色和深色主题
- 支持自定义 BottomSheetDialog 样式

## 注意事项

### 1. 内存管理
确保在对话框关闭时清理键盘适配器：

```kotlin
fun dismiss() {
    keyboardAdapter = null // 重要：清理适配器
    dialog.dismiss()
}
```

### 2. 窗口模式设置
键盘适配器会自动设置合适的软键盘模式：

```kotlin
window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
```

### 3. 布局要求
确保对话框布局中的操作按钮位于底部，这样适配器才能正确计算遮挡情况。

## 故障排除

### 键盘适配不生效
1. 检查是否正确初始化了 BottomSheetKeyboardAdapter
2. 确认对话框使用的是 BottomSheetDialog
3. 验证布局中是否有底部操作按钮

### 动画效果异常
1. 检查是否在对话框关闭时清理了适配器
2. 确认没有多次初始化适配器
3. 验证对话框的 BottomSheetBehavior 设置

### 内存泄漏
1. 确保在 dismiss() 方法中设置 `keyboardAdapter = null`
2. 检查是否正确移除了键盘监听器
3. 避免在静态上下文中持有对话框引用

## 测试

项目包含完整的单元测试，位于：
- `app/src/test/java/com/haoxueren/utils/KeyboardUtilsTest.kt`

运行测试：
```bash
./gradlew test
```

## 更新日志

### v1.0.0
- 初始版本
- 支持基本的键盘适配功能
- 包含平滑动画效果
- 提供完整的资源管理
