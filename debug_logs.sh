#!/bin/bash

# PrayRecordWidget 调试日志获取脚本
# 使用方法：
# 1. 确保设备已连接并启用USB调试
# 2. 运行此脚本：./debug_logs.sh
# 3. 在应用中触发空状态相关操作
# 4. 查看输出的日志信息

echo "=== 空状态功能调试日志监控 ==="
echo "监控组件: PrayRecordWidget, MainActivity"
echo "请在应用中触发空状态相关操作，然后查看下面的日志输出"
echo "按 Ctrl+C 停止监控"
echo ""

# 清除之前的日志缓冲区
adb logcat -c

# 开始监控 PrayRecordWidget 和 MainActivity 相关日志
adb logcat -s PrayRecordWidget:D MainActivity:D | while read line; do
    echo "[$(date '+%H:%M:%S')] $line"
done
