package com.haoxueren.utils

import android.app.Activity
import android.graphics.Rect
import android.view.View
import android.view.ViewTreeObserver
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

/**
 * KeyboardUtils测试类
 */
class KeyboardUtilsTest {

    private lateinit var mockActivity: Activity
    private lateinit var mockRootView: View
    private lateinit var mockViewTreeObserver: ViewTreeObserver
    private lateinit var mockListener: KeyboardUtils.KeyboardVisibilityListener

    @Before
    fun setUp() {
        mockActivity = mockk(relaxed = true)
        mockRootView = mockk(relaxed = true)
        mockViewTreeObserver = mockk(relaxed = true)
        mockListener = mockk(relaxed = true)

        every { mockActivity.findViewById<View>(android.R.id.content) } returns mockRootView
        every { mockRootView.viewTreeObserver } returns mockViewTreeObserver
        every { mockRootView.rootView } returns mockRootView
        every { mockRootView.context } returns mockk(relaxed = true)
        every { mockRootView.context.resources } returns mockk(relaxed = true)
        every { mockRootView.context.resources.displayMetrics } returns mockk(relaxed = true)
        every { mockRootView.context.resources.displayMetrics.density } returns 3.0f
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    @Test
    fun `测试添加键盘监听器`() {
        // 准备
        val capturedListener = slot<ViewTreeObserver.OnGlobalLayoutListener>()
        every { mockViewTreeObserver.addOnGlobalLayoutListener(capture(capturedListener)) } just Runs

        // 执行
        val result = KeyboardUtils.addKeyboardVisibilityListener(mockActivity, mockListener)

        // 验证
        verify { mockViewTreeObserver.addOnGlobalLayoutListener(any()) }
        assertNotNull(result)
        assertTrue(capturedListener.isCaptured)
    }

    @Test
    fun `测试移除键盘监听器`() {
        // 准备
        val mockGlobalLayoutListener = mockk<ViewTreeObserver.OnGlobalLayoutListener>()
        every { mockViewTreeObserver.removeOnGlobalLayoutListener(mockGlobalLayoutListener) } just Runs

        // 执行
        KeyboardUtils.removeKeyboardVisibilityListener(mockRootView, mockGlobalLayoutListener)

        // 验证
        verify { mockViewTreeObserver.removeOnGlobalLayoutListener(mockGlobalLayoutListener) }
    }

    @Test
    fun `测试键盘高度检测 - 键盘显示`() {
        // 准备
        every { mockRootView.height } returns 2000
        every { mockRootView.getWindowVisibleDisplayFrame(any()) } answers {
            val rect = firstArg<Rect>()
            rect.bottom = 1200 // 模拟键盘占用800像素
        }

        // 执行
        val keyboardHeight = KeyboardUtils.getCurrentKeyboardHeight(mockRootView)

        // 验证 - 键盘高度应该是800像素
        assertEquals(800, keyboardHeight)
    }

    @Test
    fun `测试键盘高度检测 - 键盘隐藏`() {
        // 准备
        every { mockRootView.height } returns 2000
        every { mockRootView.getWindowVisibleDisplayFrame(any()) } answers {
            val rect = firstArg<Rect>()
            rect.bottom = 1950 // 模拟键盘隐藏，只有很小的差值
        }

        // 执行
        val keyboardHeight = KeyboardUtils.getCurrentKeyboardHeight(mockRootView)

        // 验证 - 键盘高度应该是0（因为小于阈值）
        assertEquals(0, keyboardHeight)
    }

    @Test
    fun `测试隐藏软键盘`() {
        // 准备
        val mockInputMethodManager = mockk<android.view.inputmethod.InputMethodManager>(relaxed = true)
        val mockCurrentFocus = mockk<View>(relaxed = true)
        val mockWindowToken = mockk<android.os.IBinder>()

        every { mockActivity.getSystemService(Activity.INPUT_METHOD_SERVICE) } returns mockInputMethodManager
        every { mockActivity.currentFocus } returns mockCurrentFocus
        every { mockCurrentFocus.windowToken } returns mockWindowToken

        // 执行
        KeyboardUtils.hideSoftKeyboard(mockActivity)

        // 验证
        verify { mockInputMethodManager.hideSoftInputFromWindow(mockWindowToken, 0) }
    }

    @Test
    fun `测试显示软键盘`() {
        // 准备
        val mockView = mockk<View>(relaxed = true)
        val mockInputMethodManager = mockk<android.view.inputmethod.InputMethodManager>(relaxed = true)
        val mockContext = mockk<android.content.Context>(relaxed = true)

        every { mockView.context } returns mockContext
        every { mockContext.getSystemService(Activity.INPUT_METHOD_SERVICE) } returns mockInputMethodManager

        // 执行
        KeyboardUtils.showSoftKeyboard(mockView)

        // 验证
        verify { mockView.requestFocus() }
        verify { 
            mockInputMethodManager.showSoftInput(
                mockView, 
                android.view.inputmethod.InputMethodManager.SHOW_IMPLICIT
            ) 
        }
    }
}
