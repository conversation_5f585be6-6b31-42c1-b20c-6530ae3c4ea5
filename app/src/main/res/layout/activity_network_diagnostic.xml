<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="网络发现诊断工具"
        android:textSize="20sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="当前设备问题：设备A能发现设备B，但设备B无法发现设备A"
        android:textSize="14sp"
        android:textColor="#FF6600"
        android:layout_marginBottom="16dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="目标设备IP："
            android:textSize="16sp"
            android:layout_gravity="center_vertical" />

        <EditText
            android:id="@+id/et_target_ip"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="例如：***********"
            android:inputType="textNoSuggestions"
            android:layout_marginStart="8dp"
            android:selectAllOnFocus="true" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <Button
            android:id="@+id/btn_diagnose"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="开始诊断"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btn_fix"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="修复问题"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <Button
        android:id="@+id/btn_clear"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="清空结果"
        android:layout_marginBottom="16dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="诊断结果："
        android:textSize="16sp"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <ScrollView
        android:id="@+id/scroll_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/diagnostic_result_background"
        android:padding="8dp">

        <TextView
            android:id="@+id/tv_result"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:fontFamily="monospace"
            android:textIsSelectable="true" />

    </ScrollView>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="使用说明：\n1. 在设备B上输入设备A的IP地址(***********)进行诊断\n2. 在设备A上输入设备B的IP地址(************)进行诊断\n3. 根据诊断结果进行相应的修复操作"
        android:textSize="12sp"
        android:textColor="#666666"
        android:layout_marginTop="8dp" />

</LinearLayout>
