<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".manage.DatabaseManageActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/theme"
        android:theme="@style/ToolbarTheme"
        app:contentInsetStart="0dp"
        app:contentInsetStartWithNavigation="0dp"
        app:navigationIcon="@drawable/ic_back_24x24"
        app:subtitle="数据库管理"
        app:subtitleTextColor="@color/white">
        
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:orientation="horizontal">
            
            <ImageButton
                android:id="@+id/btnAddGroup"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="添加分组记录"
                android:src="@drawable/ic_add_white"
                android:tint="@color/white" />
                
        </LinearLayout>
        
    </androidx.appcompat.widget.Toolbar>

    <!-- 主内容区域：全屏宽度 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/padding_default">

        <!-- 表选择和搜索区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_input_bg"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <!-- 表选择按钮 -->
            <TextView
                android:id="@+id/tableButton"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true"
                android:drawableEnd="@drawable/ic_arrow_down_24x24"
                android:drawablePadding="4dp"
                android:gravity="center_vertical"
                android:paddingStart="8dp"
                android:text="选择表"
                android:textSize="@dimen/textSize_normal" />

            <!-- 字段选择器 -->
            <TextView
                android:id="@+id/fieldButton"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true"
                android:drawableEnd="@drawable/ic_arrow_down_24x24"
                android:drawablePadding="4dp"
                android:gravity="center_vertical"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:text="All"
                android:textSize="@dimen/textSize_normal" />

            <!-- 搜索输入框 -->
            <EditText
                android:id="@+id/searchEditText"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:background="@color/transparent"
                android:gravity="center_vertical"
                android:hint="输入关键词搜索..."
                android:singleLine="true"
                android:textSize="@dimen/textSize_normal"
                android:selectAllOnFocus="true" />

            <!-- 搜索按钮 -->
            <Button
                android:id="@+id/searchButton"
                style="?attr/borderlessButtonStyle"
                android:layout_width="60dp"
                android:layout_height="40dp"
                android:background="@color/bg_tertiary"
                android:text="搜索"
                android:layout_margin="1dp"
                android:textAllCaps="false"
                android:textSize="@dimen/textSize_normal" />

        </LinearLayout>

        <!-- 查询结果列表 -->
        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/margin_default">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/resultRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    </LinearLayout>

</LinearLayout>
