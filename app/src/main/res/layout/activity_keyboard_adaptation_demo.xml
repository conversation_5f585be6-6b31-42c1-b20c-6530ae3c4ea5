<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@color/bg_primary">

    <!-- 标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="键盘适配功能演示"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/tc_primary"
        android:gravity="center"
        android:layout_marginBottom="32dp" />

    <!-- 说明文字 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="点击下面的按钮来测试BottomSheetDialog的键盘适配功能。\n\n带适配的对话框会在键盘弹出时自动向上调整位置，确保底部按钮不被遮挡。\n\n不带适配的对话框则会被键盘遮挡底部按钮。"
        android:textSize="16sp"
        android:textColor="@color/tc_secondary"
        android:lineSpacingExtra="4dp"
        android:layout_marginBottom="32dp" />

    <!-- 带键盘适配的对话框按钮 -->
    <Button
        android:id="@+id/showDialogButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="显示带键盘适配的对话框"
        android:textColor="@android:color/white"
        android:background="@drawable/shape_button_primary"
        android:padding="16dp"
        android:textSize="16sp"
        android:layout_marginBottom="16dp" />

    <!-- 不带键盘适配的对话框按钮 -->
    <Button
        android:id="@+id/showDialogWithoutAdapterButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="显示不带键盘适配的对话框（对比）"
        android:textColor="@color/tc_primary"
        android:background="@drawable/shape_button_outline"
        android:padding="16dp"
        android:textSize="16sp"
        android:layout_marginBottom="32dp" />

    <!-- 使用说明 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="使用说明："
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/tc_primary"
        android:layout_marginBottom="8dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="1. 点击任一按钮打开对话框\n2. 点击对话框中的输入框触发软键盘\n3. 观察对话框的位置变化\n4. 对比两种模式的差异"
        android:textSize="14sp"
        android:textColor="@color/tc_secondary"
        android:lineSpacingExtra="2dp" />

</LinearLayout>
