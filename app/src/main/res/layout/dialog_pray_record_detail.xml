<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="@dimen/padding_default"
    android:background="@color/bg_primary">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingBottom="@dimen/padding_default">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="记录详情"
            android:textSize="@dimen/textSize_big"
            android:textStyle="bold"
            android:textColor="@color/tc_primary" />

        <ImageView
            android:id="@+id/closeButton"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@android:drawable/ic_menu_close_clear_cancel"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="4dp"
            android:contentDescription="关闭" />

    </LinearLayout>

    <!-- 分割线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/divider"
        android:layout_marginBottom="@dimen/padding_default" />

    <!-- 详情内容 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxHeight="400dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Group ID -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingVertical="@dimen/padding_half">

                <TextView
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="组ID："
                    android:textSize="@dimen/textSize_normal"
                    android:textColor="@color/tc_secondary"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/groupIdTextView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textSize="@dimen/textSize_normal"
                    android:textColor="@color/tc_primary"
                    android:textIsSelectable="true"
                    tools:text="1" />

                <ImageButton
                                    android:id="@+id/searchGroupIdButton"
                                    android:layout_width="20dp"
                                    android:layout_height="20dp"
                                    android:layout_gravity="center_vertical"
                                    android:background="?android:attr/selectableItemBackgroundBorderless"
                                    android:src="@android:drawable/ic_menu_search"
                                    android:contentDescription="搜索组ID"
                                    android:scaleType="fitCenter" />

            </LinearLayout>

            <!-- Date -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingVertical="@dimen/padding_half">

                <TextView
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="日期："
                    android:textSize="@dimen/textSize_normal"
                    android:textColor="@color/tc_secondary"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/dateTextView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textSize="@dimen/textSize_normal"
                    android:textColor="@color/tc_primary"
                    android:textIsSelectable="true"
                    tools:text="2024-12-18" />

                <ImageButton
                                    android:id="@+id/searchDateButton"
                                    android:layout_width="20dp"
                                    android:layout_height="20dp"
                                    android:layout_gravity="center_vertical"
                                    android:background="?android:attr/selectableItemBackgroundBorderless"
                                    android:src="@android:drawable/ic_menu_search"
                                    android:contentDescription="搜索日期"
                                    android:scaleType="fitCenter" />

            </LinearLayout>

            <!-- Object ID -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingVertical="@dimen/padding_half">

                <TextView
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="对象ID："
                    android:textSize="@dimen/textSize_normal"
                    android:textColor="@color/tc_secondary"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/objectIdTextView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textSize="@dimen/textSize_normal"
                    android:textColor="@color/tc_primary"
                    android:textIsSelectable="true"
                    android:fontFamily="monospace"
                    tools:text="abc123def4" />

                <ImageButton
                                    android:id="@+id/searchObjectIdButton"
                                    android:layout_width="20dp"
                                    android:layout_height="20dp"
                                    android:layout_gravity="center_vertical"
                                    android:background="?android:attr/selectableItemBackgroundBorderless"
                                    android:src="@android:drawable/ic_menu_search"
                                    android:contentDescription="搜索对象ID"
                                    android:scaleType="fitCenter" />

            </LinearLayout>

            <!-- Pray ID -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingVertical="@dimen/padding_half">

                <TextView
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="祈祷ID："
                    android:textSize="@dimen/textSize_normal"
                    android:textColor="@color/tc_secondary"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/prayIdTextView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textSize="@dimen/textSize_normal"
                    android:textColor="@color/tc_primary"
                    android:textIsSelectable="true"
                    tools:text="202412181234" />

                <ImageButton
                                    android:id="@+id/searchPrayIdButton"
                                    android:layout_width="20dp"
                                    android:layout_height="20dp"
                                    android:layout_gravity="center_vertical"
                                    android:background="?android:attr/selectableItemBackgroundBorderless"
                                    android:src="@android:drawable/ic_menu_search"
                                    android:contentDescription="搜索祈祷ID"
                                    android:scaleType="fitCenter" />

            </LinearLayout>

            <!-- Pray Content -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingVertical="@dimen/padding_half">

                <TextView
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="祈祷内容："
                    android:textSize="@dimen/textSize_normal"
                    android:textColor="@color/tc_secondary"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/prayContentTextView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textSize="@dimen/textSize_normal"
                    android:textColor="@color/tc_primary"
                    android:textIsSelectable="true"
                    android:lineSpacingExtra="2dp"
                    tools:text="这是一段祈祷内容的示例文本，可能会比较长，需要支持多行显示和文本选择功能。" />

                <ImageButton
                                    android:id="@+id/searchPrayContentButton"
                                    android:layout_width="20dp"
                                    android:layout_height="20dp"
                                    android:layout_gravity="center_vertical"
                                    android:background="?android:attr/selectableItemBackgroundBorderless"
                                    android:src="@android:drawable/ic_menu_search"
                                    android:contentDescription="搜索祈祷内容"
                                    android:scaleType="fitCenter" />

            </LinearLayout>



        </LinearLayout>

    </ScrollView>

</LinearLayout>
