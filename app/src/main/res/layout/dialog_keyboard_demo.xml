<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/shape_bottom_sheet_bg"
    android:padding="24dp">

    <!-- 拖拽指示器 -->
    <View
        android:layout_width="40dp"
        android:layout_height="4dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="16dp"
        android:background="@color/tc_secondary"
        android:alpha="0.5" />

    <!-- 标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="键盘适配演示对话框"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@color/tc_primary"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- 输入字段容器 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxHeight="400dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 标题输入 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="标题"
                android:textSize="14sp"
                android:textColor="@color/tc_primary"
                android:layout_marginBottom="4dp" />

            <EditText
                android:id="@+id/titleEditText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_input_bg"
                android:padding="12dp"
                android:textSize="14sp"
                android:textColor="@color/tc_primary"
                android:textColorHint="@color/tc_secondary"
                android:hint="请输入标题"
                android:maxLines="1"
                android:inputType="text"
                android:layout_marginBottom="16dp" />

            <!-- 内容输入 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="内容"
                android:textSize="14sp"
                android:textColor="@color/tc_primary"
                android:layout_marginBottom="4dp" />

            <EditText
                android:id="@+id/contentEditText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_input_bg"
                android:padding="12dp"
                android:textSize="14sp"
                android:textColor="@color/tc_primary"
                android:textColorHint="@color/tc_secondary"
                android:hint="请输入内容"
                android:maxLines="5"
                android:inputType="textMultiLine"
                android:scrollbars="vertical"
                android:layout_marginBottom="16dp" />

            <!-- 备注输入 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="备注"
                android:textSize="14sp"
                android:textColor="@color/tc_primary"
                android:layout_marginBottom="4dp" />

            <EditText
                android:id="@+id/remarksEditText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_input_bg"
                android:padding="12dp"
                android:textSize="14sp"
                android:textColor="@color/tc_primary"
                android:textColorHint="@color/tc_secondary"
                android:hint="请输入备注（可选）"
                android:maxLines="3"
                android:inputType="textMultiLine"
                android:scrollbars="vertical"
                android:layout_marginBottom="16dp" />

            <!-- 额外的输入字段，用于测试长表单的键盘适配 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="额外字段1"
                android:textSize="14sp"
                android:textColor="@color/tc_primary"
                android:layout_marginBottom="4dp" />

            <EditText
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_input_bg"
                android:padding="12dp"
                android:textSize="14sp"
                android:textColor="@color/tc_primary"
                android:textColorHint="@color/tc_secondary"
                android:hint="测试字段1"
                android:maxLines="1"
                android:inputType="text"
                android:layout_marginBottom="16dp" />

            <!-- 额外的输入字段2 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="额外字段2"
                android:textSize="14sp"
                android:textColor="@color/tc_primary"
                android:layout_marginBottom="4dp" />

            <EditText
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_input_bg"
                android:padding="12dp"
                android:textSize="14sp"
                android:textColor="@color/tc_primary"
                android:textColorHint="@color/tc_secondary"
                android:hint="测试字段2"
                android:maxLines="1"
                android:inputType="text"
                android:layout_marginBottom="16dp" />

        </LinearLayout>

    </ScrollView>

    <!-- 按钮容器 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="24dp">

        <Button
            android:id="@+id/cancelButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="取消"
            android:textColor="@color/tc_primary"
            android:background="@drawable/shape_button_outline"
            android:layout_marginEnd="8dp"
            style="?android:attr/borderlessButtonStyle" />

        <Button
            android:id="@+id/confirmButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="确认"
            android:textColor="@android:color/white"
            android:background="@drawable/shape_button_primary"
            android:layout_marginStart="8dp" />

    </LinearLayout>

</LinearLayout>
