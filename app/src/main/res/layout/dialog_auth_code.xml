<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/theme"
        android:gravity="center"
        android:padding="@dimen/padding_default"
        android:text="校验码"
        android:textColor="@color/bg_primary" />

    <EditText
        android:id="@+id/authCodeView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/margin_default"
        android:background="@drawable/shape_bg_border"
        android:gravity="center"
        android:hint="请输入2FA检验码"
        android:inputType="numberPassword"
        android:minWidth="300dp"
        android:padding="@dimen/padding_default"
        android:textSize="@dimen/textSize_normal"
        android:selectAllOnFocus="true" />

    <Button
        android:id="@+id/confirmView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/margin_half"
        android:layout_marginRight="@dimen/margin_half"
        android:layout_marginBottom="@dimen/margin_half"
        android:text="确定" />

</LinearLayout>