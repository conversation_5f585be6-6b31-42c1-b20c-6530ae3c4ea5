package com.haoxueren.demo

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.haoxueren.pray.R
import com.haoxueren.utils.BottomSheetKeyboardAdapter
import com.haoxueren.utils.ToastUtils

/**
 * 键盘适配功能演示Activity
 * 用于测试和演示BottomSheetDialog的键盘适配功能
 */
class KeyboardAdaptationDemoActivity : AppCompatActivity() {

    private lateinit var showDialogButton: Button
    private lateinit var showDialogWithoutAdapterButton: Button

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_keyboard_adaptation_demo)

        initViews()
        setupButtons()
    }

    private fun initViews() {
        showDialogButton = findViewById(R.id.showDialogButton)
        showDialogWithoutAdapterButton = findViewById(R.id.showDialogWithoutAdapterButton)
    }

    private fun setupButtons() {
        showDialogButton.setOnClickListener {
            showDialogWithKeyboardAdaptation()
        }

        showDialogWithoutAdapterButton.setOnClickListener {
            showDialogWithoutKeyboardAdaptation()
        }
    }

    /**
     * 显示带键盘适配的对话框
     */
    private fun showDialogWithKeyboardAdaptation() {
        val dialog = BottomSheetDialog(this, R.style.BottomSheetDialogTheme)
        val view = createDemoDialogView()
        dialog.setContentView(view)

        // 设置键盘适配
        val keyboardAdapter = BottomSheetKeyboardAdapter(dialog)
        keyboardAdapter.setupKeyboardAdaptation()

        // 设置底部弹窗为全屏宽度
        dialog.setOnShowListener { dialogInterface ->
            val bottomSheetDialog = dialogInterface as BottomSheetDialog
            val bottomSheet = bottomSheetDialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.let {
                val layoutParams = it.layoutParams
                layoutParams.width = android.view.ViewGroup.LayoutParams.MATCH_PARENT
                it.layoutParams = layoutParams
            }
        }

        setupDemoDialogActions(view, dialog)
        dialog.show()
    }

    /**
     * 显示不带键盘适配的对话框（用于对比）
     */
    private fun showDialogWithoutKeyboardAdaptation() {
        val dialog = BottomSheetDialog(this, R.style.BottomSheetDialogTheme)
        val view = createDemoDialogView()
        dialog.setContentView(view)

        // 设置底部弹窗为全屏宽度
        dialog.setOnShowListener { dialogInterface ->
            val bottomSheetDialog = dialogInterface as BottomSheetDialog
            val bottomSheet = bottomSheetDialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.let {
                val layoutParams = it.layoutParams
                layoutParams.width = android.view.ViewGroup.LayoutParams.MATCH_PARENT
                it.layoutParams = layoutParams
            }
        }

        setupDemoDialogActions(view, dialog)
        dialog.show()
    }

    /**
     * 创建演示对话框的视图
     */
    private fun createDemoDialogView(): View {
        val view = LayoutInflater.from(this).inflate(R.layout.dialog_keyboard_demo, null)
        return view
    }

    /**
     * 设置演示对话框的操作
     */
    private fun setupDemoDialogActions(view: View, dialog: BottomSheetDialog) {
        val titleEditText = view.findViewById<EditText>(R.id.titleEditText)
        val contentEditText = view.findViewById<EditText>(R.id.contentEditText)
        val remarksEditText = view.findViewById<EditText>(R.id.remarksEditText)
        val confirmButton = view.findViewById<Button>(R.id.confirmButton)
        val cancelButton = view.findViewById<Button>(R.id.cancelButton)

        confirmButton.setOnClickListener {
            val title = titleEditText.text.toString().trim()
            val content = contentEditText.text.toString().trim()
            val remarks = remarksEditText.text.toString().trim()

            if (title.isEmpty()) {
                ToastUtils.showToast("请输入标题")
                titleEditText.requestFocus()
                return@setOnClickListener
            }

            if (content.isEmpty()) {
                ToastUtils.showToast("请输入内容")
                contentEditText.requestFocus()
                return@setOnClickListener
            }

            ToastUtils.showToast("保存成功！\n标题：$title\n内容：$content\n备注：$remarks")
            dialog.dismiss()
        }

        cancelButton.setOnClickListener {
            dialog.dismiss()
        }
    }
}
