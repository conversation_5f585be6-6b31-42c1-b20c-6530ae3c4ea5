package com.haoxueren.utils

import android.app.Activity
import android.graphics.Rect
import android.view.View
import android.view.ViewTreeObserver
import android.view.Window
import android.view.WindowManager
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat

/**
 * 键盘工具类
 * 用于监听软键盘的显示和隐藏，以及获取键盘高度
 */
object KeyboardUtils {

    /**
     * 键盘状态监听器
     */
    interface KeyboardVisibilityListener {
        /**
         * 键盘显示
         * @param keyboardHeight 键盘高度
         */
        fun onKeyboardShow(keyboardHeight: Int)

        /**
         * 键盘隐藏
         */
        fun onKeyboardHide()
    }

    /**
     * 为Activity添加键盘监听
     * @param activity 目标Activity
     * @param listener 键盘状态监听器
     * @return 返回监听器对象，用于后续移除监听
     */
    fun addKeyboardVisibilityListener(
        activity: Activity,
        listener: KeyboardVisibilityListener
    ): ViewTreeObserver.OnGlobalLayoutListener {
        val rootView = activity.findViewById<View>(android.R.id.content)
        return addKeyboardVisibilityListener(rootView, listener)
    }

    /**
     * 为指定View添加键盘监听
     * @param rootView 根视图
     * @param listener 键盘状态监听器
     * @return 返回监听器对象，用于后续移除监听
     */
    fun addKeyboardVisibilityListener(
        rootView: View,
        listener: KeyboardVisibilityListener
    ): ViewTreeObserver.OnGlobalLayoutListener {
        var isKeyboardShowing = false
        var lastKeyboardHeight = 0

        val globalLayoutListener = ViewTreeObserver.OnGlobalLayoutListener {
            val rect = Rect()
            rootView.getWindowVisibleDisplayFrame(rect)
            
            val screenHeight = rootView.rootView.height
            val keypadHeight = screenHeight - rect.bottom
            
            // 键盘高度阈值，通常大于150dp认为是键盘显示
            val threshold = (150 * rootView.context.resources.displayMetrics.density).toInt()
            
            if (keypadHeight > threshold) {
                // 键盘显示
                if (!isKeyboardShowing || keypadHeight != lastKeyboardHeight) {
                    isKeyboardShowing = true
                    lastKeyboardHeight = keypadHeight
                    listener.onKeyboardShow(keypadHeight)
                }
            } else {
                // 键盘隐藏
                if (isKeyboardShowing) {
                    isKeyboardShowing = false
                    lastKeyboardHeight = 0
                    listener.onKeyboardHide()
                }
            }
        }

        rootView.viewTreeObserver.addOnGlobalLayoutListener(globalLayoutListener)
        return globalLayoutListener
    }

    /**
     * 移除键盘监听
     * @param rootView 根视图
     * @param listener 要移除的监听器
     */
    fun removeKeyboardVisibilityListener(
        rootView: View,
        listener: ViewTreeObserver.OnGlobalLayoutListener
    ) {
        rootView.viewTreeObserver.removeOnGlobalLayoutListener(listener)
    }

    /**
     * 获取当前键盘高度
     * @param rootView 根视图
     * @return 键盘高度，如果键盘未显示则返回0
     */
    fun getCurrentKeyboardHeight(rootView: View): Int {
        val rect = Rect()
        rootView.getWindowVisibleDisplayFrame(rect)
        val screenHeight = rootView.rootView.height
        val keypadHeight = screenHeight - rect.bottom
        val threshold = (150 * rootView.context.resources.displayMetrics.density).toInt()
        return if (keypadHeight > threshold) keypadHeight else 0
    }

    /**
     * 隐藏软键盘
     * @param activity 目标Activity
     */
    fun hideSoftKeyboard(activity: Activity) {
        val inputMethodManager = activity.getSystemService(Activity.INPUT_METHOD_SERVICE) 
            as android.view.inputmethod.InputMethodManager
        val currentFocus = activity.currentFocus
        if (currentFocus != null) {
            inputMethodManager.hideSoftInputFromWindow(currentFocus.windowToken, 0)
        }
    }

    /**
     * 显示软键盘
     * @param view 要获取焦点的View
     */
    fun showSoftKeyboard(view: View) {
        view.requestFocus()
        val inputMethodManager = view.context.getSystemService(Activity.INPUT_METHOD_SERVICE) 
            as android.view.inputmethod.InputMethodManager
        inputMethodManager.showSoftInput(view, android.view.inputmethod.InputMethodManager.SHOW_IMPLICIT)
    }

    /**
     * 设置窗口软键盘模式
     * @param window 目标窗口
     * @param mode 软键盘模式
     */
    fun setSoftInputMode(window: Window, mode: Int) {
        window.setSoftInputMode(mode)
    }

    /**
     * 为BottomSheetDialog设置键盘适配
     * @param dialog BottomSheetDialog实例
     * @param listener 键盘状态监听器
     */
    fun setupKeyboardAdaptationForBottomSheet(
        dialog: com.google.android.material.bottomsheet.BottomSheetDialog,
        listener: KeyboardVisibilityListener? = null
    ): ViewTreeObserver.OnGlobalLayoutListener? {
        val window = dialog.window ?: return null
        
        // 设置软键盘模式为调整大小
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        
        // 如果提供了监听器，添加键盘监听
        return listener?.let { 
            val rootView = window.decorView
            addKeyboardVisibilityListener(rootView, it)
        }
    }
}
