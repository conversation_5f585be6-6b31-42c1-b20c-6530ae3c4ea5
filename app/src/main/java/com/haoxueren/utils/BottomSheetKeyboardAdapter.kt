package com.haoxueren.utils

import android.animation.ValueAnimator
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.view.WindowManager
import android.view.animation.DecelerateInterpolator
import androidx.coordinatorlayout.widget.CoordinatorLayout
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog

/**
 * BottomSheetDialog键盘适配器
 * 用于处理BottomSheetDialog在软键盘弹出时的位置调整
 */
class BottomSheetKeyboardAdapter(
    private val dialog: BottomSheetDialog
) {
    
    private var keyboardListener: ViewTreeObserver.OnGlobalLayoutListener? = null
    private var bottomSheetBehavior: BottomSheetBehavior<*>? = null
    private var bottomSheetView: View? = null
    private var originalPeekHeight = 0
    private var isKeyboardShowing = false
    private var currentTranslationY = 0f
    
    // 动画相关
    private var currentAnimator: ValueAnimator? = null
    private val animationDuration = 300L
    
    /**
     * 设置键盘适配
     */
    fun setupKeyboardAdaptation() {
        val window = dialog.window ?: return
        
        // 设置软键盘模式
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        
        // 等待对话框显示后再设置监听
        dialog.setOnShowListener { 
            setupBottomSheetBehavior()
            setupKeyboardListener()
        }
        
        // 清理资源
        dialog.setOnDismissListener {
            cleanup()
        }
    }
    
    /**
     * 设置BottomSheet行为
     */
    private fun setupBottomSheetBehavior() {
        val bottomSheet = dialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
        bottomSheet?.let { sheet ->
            bottomSheetView = sheet
            val layoutParams = sheet.layoutParams as? CoordinatorLayout.LayoutParams
            val behavior = layoutParams?.behavior as? BottomSheetBehavior<*>
            
            behavior?.let { 
                bottomSheetBehavior = it
                originalPeekHeight = it.peekHeight
                
                // 设置为可拖拽但不可隐藏
                it.isHideable = false
                it.isDraggable = true
                
                // 设置状态回调
                it.addBottomSheetCallback(object : BottomSheetBehavior.BottomSheetCallback() {
                    override fun onStateChanged(bottomSheet: View, newState: Int) {
                        // 可以在这里处理状态变化
                    }
                    
                    override fun onSlide(bottomSheet: View, slideOffset: Float) {
                        // 可以在这里处理滑动
                    }
                })
            }
        }
    }
    
    /**
     * 设置键盘监听
     */
    private fun setupKeyboardListener() {
        val window = dialog.window ?: return
        val rootView = window.decorView
        
        keyboardListener = KeyboardUtils.addKeyboardVisibilityListener(
            rootView,
            object : KeyboardUtils.KeyboardVisibilityListener {
                override fun onKeyboardShow(keyboardHeight: Int) {
                    handleKeyboardShow(keyboardHeight)
                }
                
                override fun onKeyboardHide() {
                    handleKeyboardHide()
                }
            }
        )
    }
    
    /**
     * 处理键盘显示
     */
    private fun handleKeyboardShow(keyboardHeight: Int) {
        if (isKeyboardShowing) return
        
        isKeyboardShowing = true
        val bottomSheet = bottomSheetView ?: return
        
        // 计算需要向上移动的距离
        val location = IntArray(2)
        bottomSheet.getLocationOnScreen(location)
        val bottomSheetBottom = location[1] + bottomSheet.height
        
        val screenHeight = bottomSheet.context.resources.displayMetrics.heightPixels
        val availableHeight = screenHeight - keyboardHeight
        
        // 如果底部按钮被键盘遮挡，计算需要移动的距离
        val overlap = bottomSheetBottom - availableHeight
        if (overlap > 0) {
            // 添加一些额外的边距确保按钮完全可见
            val extraMargin = (16 * bottomSheet.context.resources.displayMetrics.density).toInt()
            val targetTranslationY = -(overlap + extraMargin).toFloat()
            
            animateBottomSheet(currentTranslationY, targetTranslationY)
            currentTranslationY = targetTranslationY
        }
    }
    
    /**
     * 处理键盘隐藏
     */
    private fun handleKeyboardHide() {
        if (!isKeyboardShowing) return
        
        isKeyboardShowing = false
        
        // 恢复原始位置
        animateBottomSheet(currentTranslationY, 0f)
        currentTranslationY = 0f
    }
    
    /**
     * 动画移动BottomSheet
     */
    private fun animateBottomSheet(fromY: Float, toY: Float) {
        val bottomSheet = bottomSheetView ?: return
        
        // 取消当前动画
        currentAnimator?.cancel()
        
        currentAnimator = ValueAnimator.ofFloat(fromY, toY).apply {
            duration = animationDuration
            interpolator = DecelerateInterpolator()
            
            addUpdateListener { animator ->
                val translationY = animator.animatedValue as Float
                bottomSheet.translationY = translationY
            }
            
            start()
        }
    }
    
    /**
     * 清理资源
     */
    private fun cleanup() {
        keyboardListener?.let { listener ->
            val window = dialog.window
            if (window != null) {
                KeyboardUtils.removeKeyboardVisibilityListener(window.decorView, listener)
            }
        }
        keyboardListener = null
        
        currentAnimator?.cancel()
        currentAnimator = null
        
        // 恢复原始状态
        bottomSheetView?.translationY = 0f
        bottomSheetBehavior?.peekHeight = originalPeekHeight
        
        bottomSheetBehavior = null
        bottomSheetView = null
    }
    
    /**
     * 手动触发键盘适配检查
     * 在某些情况下可能需要手动调用
     */
    fun checkKeyboardState() {
        val window = dialog.window ?: return
        val rootView = window.decorView
        val keyboardHeight = KeyboardUtils.getCurrentKeyboardHeight(rootView)
        
        if (keyboardHeight > 0) {
            handleKeyboardShow(keyboardHeight)
        } else {
            handleKeyboardHide()
        }
    }
    
    /**
     * 设置动画持续时间
     */
    fun setAnimationDuration(duration: Long) {
        // 可以通过这个方法自定义动画时长
    }
}
