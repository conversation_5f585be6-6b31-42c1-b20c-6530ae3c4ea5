package com.haoxueren.widget

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.haoxueren.pray.R
import com.haoxueren.pray.bean.HaoPray
import com.haoxueren.pray.service.DataService
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers

/**
 * 祈祷记录详情对话框
 * 使用 BottomSheetDialog 实现从底部向上弹出的效果
 */
@SuppressLint("CheckResult")
class PrayRecordDetailDialog(
    private val context: Context,
    private val record: HaoPray
) {
    
    private lateinit var dialog: BottomSheetDialog
    private lateinit var groupIdTextView: TextView
    private lateinit var prayIdTextView: TextView
    private lateinit var dateTextView: TextView
    private lateinit var prayContentTextView: TextView
    
    // 保存原始日期值用于搜索
    private var originalDate: String = ""
    private lateinit var objectIdTextView: TextView
    private lateinit var closeButton: ImageView
    
    // 搜索按钮
    private lateinit var searchGroupIdButton: android.widget.ImageButton
    private lateinit var searchPrayIdButton: android.widget.ImageButton
    private lateinit var searchDateButton: android.widget.ImageButton
    private lateinit var searchPrayContentButton: android.widget.ImageButton
    private lateinit var searchObjectIdButton: android.widget.ImageButton
    
    fun show() {
        // 使用主题感知的BottomSheetDialog
        dialog = BottomSheetDialog(context, R.style.BottomSheetDialogTheme)
        val view = LayoutInflater.from(context).inflate(R.layout.dialog_pray_record_detail, null)
        dialog.setContentView(view)

        // 设置底部弹窗为全屏宽度
        dialog.setOnShowListener { dialogInterface ->
            val bottomSheetDialog = dialogInterface as BottomSheetDialog
            val bottomSheet = bottomSheetDialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.let {
                val layoutParams = it.layoutParams
                layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT
                it.layoutParams = layoutParams
            }
        }

        initViews(view)
        setupCloseButton()
        setupSearchButtons()
        loadRecordDetails()

        dialog.show()
    }
    
    private fun initViews(view: View) {
        groupIdTextView = view.findViewById(R.id.groupIdTextView)
        prayIdTextView = view.findViewById(R.id.prayIdTextView)
        dateTextView = view.findViewById(R.id.dateTextView)
        prayContentTextView = view.findViewById(R.id.prayContentTextView)
        objectIdTextView = view.findViewById(R.id.objectIdTextView)
        closeButton = view.findViewById(R.id.closeButton)
        
        // 初始化搜索按钮
        searchGroupIdButton = view.findViewById(R.id.searchGroupIdButton)
        searchPrayIdButton = view.findViewById(R.id.searchPrayIdButton)
        searchDateButton = view.findViewById(R.id.searchDateButton)
        searchPrayContentButton = view.findViewById(R.id.searchPrayContentButton)
        searchObjectIdButton = view.findViewById(R.id.searchObjectIdButton)
    }
    
    private fun setupCloseButton() {
        closeButton.setOnClickListener {
            dialog.dismiss()
        }
        
        // 点击外部区域关闭对话框
        dialog.setCanceledOnTouchOutside(true)
    }
    
    private fun setupSearchButtons() {
        searchGroupIdButton.setOnClickListener {
            val groupId = groupIdTextView.text.toString()
            if (groupId.isNotEmpty() && groupId != "未知" && groupId != "未分组" && groupId != "获取失败") {
                // 关闭对话框
                dialog.dismiss()
                com.haoxueren.pray.manage.DatabaseManageActivity.startWithSearch(
                    context,
                    "HaoGroup",
                    "groupId",
                    groupId
                )
            }
        }
        
        searchPrayIdButton.setOnClickListener {
            val prayId = prayIdTextView.text.toString()
            if (prayId.isNotEmpty() && prayId != "未知") {
                // 关闭对话框
                dialog.dismiss()
                com.haoxueren.pray.manage.DatabaseManageActivity.startWithSearch(
                    context,
                    "HaoPray",
                    "id",
                    prayId
                )
            }
        }
        
        searchDateButton.setOnClickListener {
            // 使用原始日期值进行搜索，而不是拼接后的值
            if (originalDate.isNotEmpty() && originalDate != "未知") {
                // 关闭对话框
                dialog.dismiss()
                com.haoxueren.pray.manage.DatabaseManageActivity.startWithSearch(
                    context,
                    "HaoPray",
                    "date",
                    originalDate
                )
            }
        }
        
        searchPrayContentButton.setOnClickListener {
            val prayContent = prayContentTextView.text.toString()
            if (prayContent.isNotEmpty() && prayContent != "无内容") {
                // 关闭对话框
                dialog.dismiss()
                com.haoxueren.pray.manage.DatabaseManageActivity.startWithSearch(
                    context,
                    "HaoPray",
                    "pray",
                    prayContent
                )
            }
        }
        
        searchObjectIdButton.setOnClickListener {
            val objectId = objectIdTextView.text.toString()
            if (objectId.isNotEmpty() && objectId != "未知") {
                // 关闭对话框
                dialog.dismiss()
                // 对于objectId，我们可以在两个表中搜索
                // 先尝试在HaoPray表中搜索
                com.haoxueren.pray.manage.DatabaseManageActivity.startWithSearch(
                    context,
                    "HaoPray",
                    "objectId",
                    objectId
                )
            }
        }
    }
    
    private fun loadRecordDetails() {
        // 设置基本信息
        prayIdTextView.text = record.id ?: "未知"
        // 保存原始日期值用于搜索
        originalDate = record.date ?: "未知"
        // 将日期和次数拼接成"2025.06.30.1130"格式
        val date = record.date ?: "未知"
        val count = record.count?.toString() ?: "0"
        dateTextView.text = if (date != "未知") "$date.$count" else date
        prayContentTextView.text = record.pray ?: "无内容"
        objectIdTextView.text = record.objectId ?: "未知"
        
        // 异步获取 groupId
        loadGroupId()
    }
    
    private fun loadGroupId() {
        val prayId = record.id
        if (prayId.isNullOrEmpty()) {
            groupIdTextView.text = "未知"
            return
        }
        
        DataService.getInstance().getGroupId(prayId)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(
                { groupId ->
                    groupIdTextView.text = groupId?.toString() ?: "未分组"
                },
                { error ->
                    error.printStackTrace()
                    groupIdTextView.text = "获取失败"
                }
            )
    }
    
    companion object {
        /**
         * 显示记录详情对话框
         * @param context 上下文
         * @param record 要显示的记录
         */
        @JvmStatic
        fun show(context: Context, record: HaoPray) {
            val dialog = PrayRecordDetailDialog(context, record)
            dialog.show()
        }
    }
}
