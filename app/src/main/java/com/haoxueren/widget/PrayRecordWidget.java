package com.haoxueren.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.os.Vibrator;
import android.util.AttributeSet;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.haoxueren.helper.PageLoadHelper;
import com.haoxueren.pray.bean.HaoPray;
import com.haoxueren.pray.R;
import com.haoxueren.pray.main.MainPresenter;
import com.haoxueren.pray.service.DataService;
import com.haoxueren.proxy.RecyclerViewProxy;
import com.haoxueren.proxy.SuperViewHolder;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.constant.RefreshState;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.function.Consumer;

@SuppressLint("CheckResult")
public class PrayRecordWidget extends FrameLayout {

    FrameLayout containerLayout;
    SmartRefreshLayout refreshLayout;
    RecyclerViewProxy<HaoPray> recyclerView;
    View emptyStateView;
    private boolean isEmptyStateAdded = false; // 跟踪空状态视图是否已添加

    private String prayId = "";
    private final int size = 20;
    private List<HaoPray> recordList = new ArrayList<>();
    private MainPresenter presenter = MainPresenter.getInstance();
    private PageLoadHelper<HaoPray> loadHelper = new PageLoadHelper<>(recordList, size);

    public PrayRecordWidget(@NonNull Context context) {
        this(context, null);
    }

    public PrayRecordWidget(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);

        View.inflate(context, R.layout.widget_pray_record, this);
        containerLayout = findViewById(R.id.containerLayout);
        refreshLayout = findViewById(R.id.refreshLayout);

        // 初始化空状态视图
        try {
            emptyStateView = View.inflate(context, R.layout.layout_pray_record_empty, null);
        } catch (Exception e) {
            // 忽略空状态视图创建错误
        }

        recyclerView = new RecyclerViewProxy<HaoPray>(this, R.id.recyclerView) {
            @Override
            protected SuperViewHolder<HaoPray> onCreateHolder(ViewGroup parent, int viewType) {
                return new SuperViewHolder<HaoPray>(parent, R.layout.item_pray_record) {

                    TextView recordTextView;

                    @Override
                    public void initView(View layout) {
                        recordTextView = itemView.findViewById(R.id.recordTextView);

                        // 添加长按监听器显示详情对话框
                        recordTextView.setOnLongClickListener(v -> {
                            int position = getBindingAdapterPosition();
                            if (position != -1 && position < getAdapterList().size()) {
                                HaoPray record = getAdapterList().get(position);

                                // 添加震动反馈
                                try {
                                    Vibrator vibrator = (Vibrator) getContext().getSystemService(Context.VIBRATOR_SERVICE);
                                    if (vibrator != null && vibrator.hasVibrator()) {
                                        vibrator.vibrate(50); // 震动50毫秒
                                    }
                                } catch (Exception e) {
                                    // 忽略震动错误
                                }

                                // 显示详情对话框
                                PrayRecordDetailDialog.show(getContext(), record);
                            }
                            return true; // 返回true表示消费了长按事件
                        });
                    }

                    @Override
                    public void updateItem(HaoPray bean) {
                        String record = String.format(Locale.CHINESE, "%s.%d %s", bean.getDate(), bean.getCount(), bean.getPray());
                        recordTextView.setText(record);
                    }
                };
            }
        };
        recyclerView.setAdapter(recordList);

        initRefreshLayout();
    }

    private void initRefreshLayout() {
        refreshLayout.setOnRefreshListener(layout -> {
            // 确保只刷新当前分组的数据
            if (prayId != null && !prayId.isEmpty()) {
                loadHelper.onRefresh((page, size) -> {
                    loadRecord(prayId, 0);
                });
            } else {
                // 如果没有当前分组ID，直接完成刷新
                refreshLayout.finishRefresh();
            }
        });
        refreshLayout.setOnLoadMoreListener(layout -> {
            // 确保只加载当前分组的更多数据
            if (prayId != null && !prayId.isEmpty() && loadHelper.hasMoreData()) {
                loadHelper.onLoadMore((page, size) -> {
                    int skip = (page - 1) * size;
                    loadRecord(prayId, skip);
                });
            } else {
                refreshLayout.finishLoadMoreWithNoMoreData();
            }
        });
    }

    public void loadRecord(String id, int skip) {
        this.prayId = id;

        presenter.queryRecord(id, skip, size)
                .subscribe(this::onRecordSuccess, this::onBmobFailure);
    }

    public void deleteRecord(int position) {
        HaoPray haoPray = recordList.get(position);
        presenter.deletePrayRecord(haoPray).subscribe(
                onDeleteSuccess(position)::accept, this::onBmobFailure);
    }

    public Consumer<String> onDeleteSuccess(int position) {
        recordList.remove(position);

        recyclerView.notifyItemRemoved(position);
        Toast.makeText(getContext(), "删除成功", Toast.LENGTH_SHORT).show();

        // 删除后检查是否需要显示空状态
        updateEmptyState();

        return System.out::println;
    }

    private void onRecordSuccess(List<HaoPray> list) {
        RefreshState state = refreshLayout.getState();

        int oldSize = recordList.size();
        loadHelper.onSuccess(list, state == RefreshState.Loading, () -> {
            recyclerView.notifyDataSetChanged();
            refreshLayout.finishRefresh();
            refreshLayout.finishLoadMore();

            // 检查并显示/隐藏空状态视图
            updateEmptyState();
        });
    }

    public void onBmobFailure(Throwable e) {
        e.printStackTrace();
        refreshLayout.finishRefresh();
        refreshLayout.finishLoadMore();
        Toast.makeText(getContext(), e.getMessage(), Toast.LENGTH_SHORT).show();
    }

    public void scrollToTop() {
        recyclerView.scrollToPosition(0);
    }

    /**
     * 更新空状态视图的显示/隐藏
     */
    private void updateEmptyState() {
        boolean isEmpty = recordList.isEmpty();

        if (emptyStateView == null) {
            return;
        }

        // 检查当前空状态视图的可见性，避免不必要的操作
        boolean isCurrentlyVisible = emptyStateView.getVisibility() == View.VISIBLE;

        if (isEmpty) {
            // 显示空状态视图
            if (!isEmptyStateAdded) {
                // 将空状态视图添加到容器布局中，覆盖在 SmartRefreshLayout 上方
                FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(
                    FrameLayout.LayoutParams.MATCH_PARENT,
                    FrameLayout.LayoutParams.MATCH_PARENT
                );
                try {
                    containerLayout.addView(emptyStateView, params);
                    isEmptyStateAdded = true;
                } catch (Exception e) {
                    // 忽略添加错误
                }
            }

            // 只在需要时更新可见性，避免不必要的UI操作
            if (!isCurrentlyVisible) {
                emptyStateView.setVisibility(View.VISIBLE);
            }

            recyclerView.setVisible(false);

            // 在空状态下禁用加载更多，但保留刷新功能
            refreshLayout.setEnableLoadMore(false);
            refreshLayout.setEnableRefresh(true);
        } else {
            // 只在需要时隐藏空状态视图
            if (isCurrentlyVisible) {
                emptyStateView.setVisibility(View.GONE);
            }

            recyclerView.setVisible(true);

            // 有数据时启用刷新和加载更多功能
            refreshLayout.setEnableRefresh(true);
            refreshLayout.setEnableLoadMore(true);
        }
    }

    /**
     * 显示空状态（供外部调用）
     */
    public void showEmptyState() {
        recordList.clear();

        recyclerView.notifyDataSetChanged();

        updateEmptyState();

        // 在空状态下禁用加载更多，但保留刷新功能
        refreshLayout.setEnableLoadMore(false);
        refreshLayout.setEnableRefresh(true);
    }

    /**
     * 显示空状态并更新prayId（推荐使用）
     */
    public void showEmptyState(String prayId) {
        updatePrayId(prayId);
        updateEmptyStateInfo(prayId);
        showEmptyState();
    }

    /**
     * 更新空状态界面中的分组信息
     */
    private void updateEmptyStateInfo(String prayId) {
        if (emptyStateView == null) {
            return;
        }

        // 更新分组ID
        TextView tvPrayId = emptyStateView.findViewById(R.id.tv_pray_id);
        if (tvPrayId != null) {
            tvPrayId.setText(prayId != null ? prayId : "未知分组");
        }

        // 获取并更新祈祷内容
        TextView tvPrayContent = emptyStateView.findViewById(R.id.tv_pray_content);
        if (tvPrayContent != null && prayId != null) {
            DataService.getInstance().getPrayContent(prayId)
                    .subscribe(
                        content -> {
                            if (content != null && !content.trim().isEmpty()) {
                                tvPrayContent.setText(content);
                            } else {
                                tvPrayContent.setText("暂无祈祷内容");
                            }
                        },
                        error -> {
                            tvPrayContent.setText("获取祈祷内容失败");
                        }
                    );
        }
    }

    /**
     * 更新当前的prayId（确保数据同步）
     */
    public void updatePrayId(String newPrayId) {
        this.prayId = newPrayId;
    }

    /**
     * 清理空状态视图（用于组件重置或内存清理）
     */
    public void clearEmptyState() {
        if (isEmptyStateAdded && emptyStateView != null && emptyStateView.getParent() != null) {
            containerLayout.removeView(emptyStateView);
            isEmptyStateAdded = false;
        }
    }
}
