package com.haoxueren.pray.manage

import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.graphics.drawable.LayerDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.haoxueren.pray.R
import com.haoxueren.view.HorizontalScrollTextView

/**
 * 数据库查询结果适配器
 */
class DatabaseRecordAdapter(
    private val onRecordClick: (DatabaseRecord) -> Unit
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private val records = mutableListOf<DatabaseRecord>()
    private var showEmpty = true

    companion object {
        private const val TYPE_EMPTY = 0
        private const val TYPE_RECORD = 1
        private const val TYPE_GROUP_RECORD = 2
    }

    fun updateRecords(newRecords: List<DatabaseRecord>) {
        records.clear()
        records.addAll(newRecords)
        showEmpty = records.isEmpty()
        notifyDataSetChanged()
    }

    fun addRecords(newRecords: List<DatabaseRecord>) {
        val startPosition = records.size
        records.addAll(newRecords)
        showEmpty = false
        notifyItemRangeInserted(startPosition, newRecords.size)
    }

    override fun getItemViewType(position: Int): Int {
        if (showEmpty && records.isEmpty()) {
            return TYPE_EMPTY
        }
        val record = records[position]
        return if (record.rawData["tableName"] == DatabaseTable.TABLE_HAO_GROUP) {
            TYPE_GROUP_RECORD
        } else {
            TYPE_RECORD
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_EMPTY -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.layout_database_empty, parent, false)
                EmptyViewHolder(view)
            }
            TYPE_GROUP_RECORD -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_database_group_record, parent, false)
                GroupRecordViewHolder(view)
            }
            else -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_database_record, parent, false)
                RecordViewHolder(view)
            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is RecordViewHolder -> {
                val record = records[position]
                holder.bind(record, onRecordClick)
            }
            is GroupRecordViewHolder -> {
                val record = records[position]
                holder.bind(record, onRecordClick)
            }
            is EmptyViewHolder -> {
                // 空状态不需要绑定数据
            }
        }
    }

    override fun getItemCount(): Int = if (showEmpty && records.isEmpty()) 1 else records.size

    class RecordViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val primaryFieldTextView: TextView = itemView.findViewById(R.id.primaryFieldTextView)

        fun bind(record: DatabaseRecord, onRecordClick: (DatabaseRecord) -> Unit) {
            primaryFieldTextView.text = record.primaryField

            // 根据prayId设置背景色
            val tableName = record.rawData["tableName"]?.toString() ?: ""
            if (tableName == DatabaseTable.TABLE_HAO_PRAY) {
                // 使用prayId字段生成颜色，与pray内容无关
                val prayId = record.rawData["id"]?.toString() ?: ""  // HaoPray表中prayId字段名为"id"
                if (prayId.isNotEmpty()) {
                    val color = generateColorFromPrayId(prayId)
                    itemView.setBackgroundColor(color)
                } else {
                    // 如果没有prayId，保持默认背景
                    itemView.setBackgroundResource(R.drawable.shape_pray_item_background)
                }
            }

            // 改为长按事件
            itemView.setOnLongClickListener {
                onRecordClick(record)
                true // 返回true表示消费了长按事件
            }
        }

        /**
         * 根据prayId生成颜色
         */
        private fun generateColorFromPrayId(prayId: String): Int {
            // 尝试解析prayId为数字，如果失败则使用字符串hash
            val numericId = try {
                prayId.toLong()
            } catch (e: NumberFormatException) {
                prayId.hashCode().toLong()
            }
            
            // 使用改进的哈希算法，使相邻值产生明显不同的颜色
            var hash = numericId
            hash = hash xor (hash shr 32)
            hash = hash xor (hash shr 16)
            hash = hash xor (hash shr 8)
            
            // 引入黄金比例常数来更好地分散值
            val goldenRatio = 0.618033988749895
            val spreadValue = (numericId * goldenRatio * 1000).toLong()
            hash = hash xor spreadValue
            
            // 使用更复杂的位运算来生成RGB值
            val red = ((hash and 0xFF0000) shr 16).toInt()
            val green = ((hash and 0x00FF00) shr 8).toInt()
            val blue = (hash and 0x0000FF).toInt()
            
            // 确保颜色值在合理范围内
            val finalRed = ((red * 17) % 256).toInt()
            val finalGreen = ((green * 19) % 256).toInt()  
            val finalBlue = ((blue * 23) % 256).toInt()
            
            // 设置透明度，使颜色更柔和
            val alpha = 100 // 半透明效果
            return (alpha shl 24) or (finalRed shl 16) or (finalGreen shl 8) or finalBlue
        }
    }

    class GroupRecordViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val groupIdView: TextView = itemView.findViewById(R.id.groupIdView)
        private val prayTextView: HorizontalScrollTextView = itemView.findViewById(R.id.prayTextView)

        fun bind(record: DatabaseRecord, onRecordClick: (DatabaseRecord) -> Unit) {
            groupIdView.text = String.format("%s.", record.rawData["groupId"]?.toString() ?: "")
            prayTextView.setText(record.primaryField)

            // 根据groupId设置背景色
            val groupId = record.rawData["groupId"]?.toString() ?: ""
            if (groupId.isNotEmpty()) {
                val color = generateColorFromGroupId(groupId)
                val backgroundDrawable = createColoredBackgroundWithBorder(color)
                itemView.setBackground(backgroundDrawable)
            } else {
                // 如果没有groupId，使用默认背景
                itemView.setBackgroundResource(R.drawable.shape_group_item_background)
            }

            // 为整个itemView设置长按事件
            itemView.setOnLongClickListener {
                onRecordClick(record)
                true // 返回true表示消费了长按事件
            }

            // 为groupIdView也设置长按事件
            groupIdView.setOnLongClickListener {
                onRecordClick(record)
                true
            }

            // 为prayTextView设置长按事件
            prayTextView.setOnLongClickListener {
                onRecordClick(record)
                true
            }

            // 优化prayTextView的触摸行为，保持横向滑动功能
            prayTextView.isFocusable = false
            prayTextView.isFocusableInTouchMode = false
            prayTextView.isClickable = false // 禁用点击，只保留长按
        }

        /**
         * 根据groupId生成颜色
         */
        private fun generateColorFromGroupId(groupId: String): Int {
            // 尝试解析groupId为数字，如果失败则使用字符串hash
            val numericId = try {
                groupId.toLong()
            } catch (e: NumberFormatException) {
                groupId.hashCode().toLong()
            }
            
            // 使用改进的哈希算法，使相邻值产生明显不同的颜色
            var hash = numericId
            hash = hash xor (hash shr 32)
            hash = hash xor (hash shr 16)
            hash = hash xor (hash shr 8)
            
            // 引入黄金比例常数来更好地分散值
            val goldenRatio = 0.618033988749895
            val spreadValue = (numericId * goldenRatio * 1000).toLong()
            hash = hash xor spreadValue
            
            // 使用更复杂的位运算来生成RGB值
            val red = ((hash and 0xFF0000) shr 16).toInt()
            val green = ((hash and 0x00FF00) shr 8).toInt()
            val blue = (hash and 0x0000FF).toInt()
            
            // 确保颜色值在合理范围内
            val finalRed = ((red * 17) % 256).toInt()
            val finalGreen = ((green * 19) % 256).toInt()  
            val finalBlue = ((blue * 23) % 256).toInt()
            
            // 设置透明度，使颜色更柔和
            val alpha = 120 // 半透明效果
            return (alpha shl 24) or (finalRed shl 16) or (finalGreen shl 8) or finalBlue
        }

        /**
         * 创建带背景色和底部边框的drawable
         */
        private fun createColoredBackgroundWithBorder(backgroundColor: Int): Drawable {
            // 简单实现：只设置背景色，保持原有的底部边框通过布局文件实现
            return ColorDrawable(backgroundColor)
        }
    }

    class EmptyViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)
}
