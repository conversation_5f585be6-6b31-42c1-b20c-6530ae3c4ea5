package com.haoxueren.pray.manage

import android.content.Context
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.TextView
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.haoxueren.pray.R
import com.haoxueren.pray.bean.HaoPray
import com.haoxueren.pray.group.HaoPrayGroup
import com.haoxueren.sqlite.SQLiteHelper
import com.haoxueren.utils.Base16
import com.haoxueren.utils.ToastUtils
import com.haoxueren.utils.BottomSheetKeyboardAdapter
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.*

/**
 * 数据库记录添加对话框
 */
class DatabaseRecordAddDialog(
    private val context: Context,
    private val tableName: String,
    private val onAddSuccess: () -> Unit
) {

    private lateinit var dialog: BottomSheetDialog
    private lateinit var fieldsContainer: LinearLayout
    private lateinit var errorTextView: TextView
    private lateinit var saveButton: Button
    private lateinit var cancelButton: Button
    
    private val fieldEditTexts = mutableMapOf<String, EditText>()
    private val compositeDisposable = CompositeDisposable()

    // 键盘适配器
    private var keyboardAdapter: BottomSheetKeyboardAdapter? = null

    // 不显示的字段（自动生成的字段）
    private val hiddenFields = setOf("objectId", "createdAt", "updatedAt")
    
    fun show() {
        // 使用主题感知的BottomSheetDialog
        dialog = BottomSheetDialog(context, R.style.BottomSheetDialogTheme)
        val view = LayoutInflater.from(context).inflate(R.layout.dialog_database_record_edit, null)
        dialog.setContentView(view)

        // 设置键盘适配
        keyboardAdapter = BottomSheetKeyboardAdapter(dialog)
        keyboardAdapter?.setupKeyboardAdaptation()

        // 设置底部弹窗为全屏宽度
        dialog.setOnShowListener { dialogInterface ->
            val bottomSheetDialog = dialogInterface as BottomSheetDialog
            val bottomSheet = bottomSheetDialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.let {
                val layoutParams = it.layoutParams
                layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT
                it.layoutParams = layoutParams
            }
        }

        initViews(view)
        setupFields()
        setupButtons()

        dialog.show()
    }
    
    private fun initViews(view: View) {
        fieldsContainer = view.findViewById(R.id.fieldsContainer)
        errorTextView = view.findViewById(R.id.errorTextView)
        saveButton = view.findViewById(R.id.saveButton)
        cancelButton = view.findViewById(R.id.cancelButton)
        
        val titleTextView = view.findViewById<TextView>(R.id.titleTextView)
        titleTextView.text = "添加 $tableName 记录"
    }
    
    private fun setupFields() {
        fieldsContainer.removeAllViews()
        fieldEditTexts.clear()
        
        // 根据表名添加相应的字段
        val fields = getFieldsForTable()
        
        for (fieldName in fields) {
            if (hiddenFields.contains(fieldName)) continue

            val fieldView = LayoutInflater.from(context).inflate(R.layout.item_edit_field, fieldsContainer, false)
            val labelTextView = fieldView.findViewById<TextView>(R.id.fieldLabelTextView)
            val editText = fieldView.findViewById<EditText>(R.id.fieldValueEditText)

            // 设置字段标签
            labelTextView.text = getFieldDisplayName(fieldName)

            // 根据字段类型设置输入类型
            setupInputType(fieldName, editText)

            // 为HaoGroup表的prayId字段设置默认值
            if (tableName == "HaoGroup" && fieldName == "prayId") {
                val defaultPrayId = com.haoxueren.utils.DateUtils.today("yyyyMMddHHmm")
                editText.setText(defaultPrayId)
            }

            fieldEditTexts[fieldName] = editText
            fieldsContainer.addView(fieldView)
        }
    }
    
    private fun getFieldsForTable(): List<String> {
        return when (tableName) {
            "HaoPray" -> listOf("id", "date", "count", "pray")
            "HaoGroup" -> listOf("groupId", "prayId", "pray")
            else -> emptyList()
        }
    }
    
    private fun getFieldDisplayName(fieldName: String): String {
        return when (fieldName) {
            "objectId" -> "对象ID"
            "id" -> "ID"
            "date" -> "日期"
            "count" -> "计数"
            "pray" -> "祷告内容"
            "groupId" -> "分组ID"
            "prayId" -> "祷告ID"
            "createdAt" -> "创建时间"
            "updatedAt" -> "更新时间"
            else -> fieldName
        }
    }
    
    private fun setupInputType(fieldName: String, editText: EditText) {
        when (fieldName) {
            "count", "groupId" -> {
                editText.inputType = android.text.InputType.TYPE_CLASS_NUMBER
            }
            "pray" -> {
                editText.inputType = android.text.InputType.TYPE_CLASS_TEXT or android.text.InputType.TYPE_TEXT_FLAG_MULTI_LINE
                editText.maxLines = 5
            }
            "date" -> {
                editText.hint = "格式: yyyy-MM-dd"
            }
            else -> {
                editText.inputType = android.text.InputType.TYPE_CLASS_TEXT
            }
        }
    }
    
    private fun setupButtons() {
        cancelButton.setOnClickListener {
            dialog.dismiss()
        }
        
        saveButton.setOnClickListener {
            saveRecord()
        }
    }
    
    private fun saveRecord() {
        hideError()
        
        // 验证输入
        val validationResult = validateInput()
        if (!validationResult.isValid) {
            showError(validationResult.errorMessage)
            return
        }
        
        // 执行添加操作
        performAdd()
    }
    
    private fun validateInput(): ValidationResult {
        for ((fieldName, editText) in fieldEditTexts) {
            if (hiddenFields.contains(fieldName)) continue

            val value = editText.text.toString().trim()

            // 验证必填字段
            if (isRequiredField(fieldName) && value.isEmpty()) {
                return ValidationResult(false, "${getFieldDisplayName(fieldName)}不能为空")
            }

            // 验证数字字段
            if (isNumericField(fieldName) && value.isNotEmpty()) {
                try {
                    value.toInt()
                } catch (e: NumberFormatException) {
                    return ValidationResult(false, "${getFieldDisplayName(fieldName)}必须是有效的数字")
                }
            }

            // 验证日期格式
            if (fieldName == "date" && value.isNotEmpty()) {
                if (!isValidDateFormat(value)) {
                    return ValidationResult(false, "日期格式不正确，应为 yyyy-MM-dd")
                }
            }
        }
        
        return ValidationResult(true, "")
    }
    
    private fun isRequiredField(fieldName: String): Boolean {
        return when (tableName) {
            "HaoPray" -> fieldName in setOf("id", "date", "pray")
            "HaoGroup" -> fieldName in setOf("groupId", "prayId")
            else -> false
        }
    }
    
    private fun isNumericField(fieldName: String): Boolean {
        return fieldName in setOf("count", "groupId")
    }
    
    private fun isValidDateFormat(date: String): Boolean {
        // 支持日期格式：yyyy-MM-dd
        return date.matches(Regex("\\d{4}-\\d{2}-\\d{2}"))
    }
    
    private fun processFieldValue(fieldName: String, value: String): Any {
        return when {
            isNumericField(fieldName) -> {
                if (value.isEmpty()) 0 else value.toInt()
            }
            fieldName == "pray" -> {
                // 对祷告内容进行Base16编码
                Base16.encode(value)
            }
            else -> value
        }
    }
    
    private fun performAdd() {
        saveButton.isEnabled = false
        saveButton.text = "保存中..."
        
        val disposable = Observable.fromCallable {
            when (tableName) {
                "HaoPray" -> {
                    val haoPray = HaoPray()
                    haoPray.setId(fieldEditTexts["id"]?.text?.toString()?.trim() ?: "")
                    haoPray.setDate(fieldEditTexts["date"]?.text?.toString()?.trim() ?: "")
                    haoPray.setCount(fieldEditTexts["count"]?.text?.toString()?.trim() ?: "0")
                    haoPray.setPray(fieldEditTexts["pray"]?.text?.toString()?.trim() ?: "")
                    
                    // 设置创建和更新时间
                    val currentTime = com.haoxueren.utils.DateUtils.today("yyyy-MM-dd HH:mm:ss")
                    haoPray.setCreatedAt(currentTime)
                    haoPray.setUpdatedAt(currentTime)
                    
                    SQLiteHelper.getInstance().insertHaoPray(haoPray).blockingFirst()
                    "祷告记录添加成功"
                }
                "HaoGroup" -> {
                    val groupId = fieldEditTexts["groupId"]?.text?.toString()?.trim()?.toIntOrNull()
                    val prayId = fieldEditTexts["prayId"]?.text?.toString()?.trim() ?: ""
                    val prayText = fieldEditTexts["pray"]?.text?.toString()?.trim() ?: ""
                    
                    if (groupId == null) {
                        throw IllegalArgumentException("分组ID不能为空")
                    }
                    
                    SQLiteHelper.getInstance().insertHaoGroup(groupId, prayId, prayText)
                    "分组记录添加成功"
                }
                else -> throw IllegalArgumentException("不支持的表: $tableName")
            }
        }
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(
                { result ->
                    ToastUtils.showToast(result)
                    dialog.dismiss()
                    onAddSuccess()
                },
                { error ->
                    showError("添加失败：${error.message}")
                    resetSaveButton()
                }
            )
        
        compositeDisposable.add(disposable)
    }
    
    private fun resetSaveButton() {
        saveButton.isEnabled = true
        saveButton.text = "保存"
    }
    
    private fun showError(message: String) {
        errorTextView.text = message
        errorTextView.visibility = View.VISIBLE
    }
    
    private fun hideError() {
        errorTextView.visibility = View.GONE
    }
    
    fun dismiss() {
        compositeDisposable.clear()
        keyboardAdapter = null // 清理键盘适配器
        if (::dialog.isInitialized && dialog.isShowing) {
            dialog.dismiss()
        }
    }
    
    private data class ValidationResult(
        val isValid: Boolean,
        val errorMessage: String
    )
}
